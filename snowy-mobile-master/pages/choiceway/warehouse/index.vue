<!--批次入库-->
<template>
    <view class="warehouse-container">
        <!-- 搜索区域 -->
        <view class="search-section">
            <view class="search-title">批次入库</view>

            <!-- 输入框区域 -->
            <view class="input-wrapper">
                <uv-icon class="icon" name="scan" size="20" color="#999"></uv-icon>
                <input
                    v-model="batchId"
                    class="input"
                    type="text"
                    placeholder="请输入批次ID"
                    maxlength="50"
                    @confirm="addBatchId"
                />
                <view class="scan-btn" @tap="scanCode">
                    <uv-icon name="scan" size="20" color="#5677fc"></uv-icon>
                </view>
            </view>

            <!-- 按钮区域 -->
            <view class="action-buttons-top">
                <uv-button
                    type="default"
                    @tap="clearAll"
                    :disabled="searching || operating"
                    size="default"
                >
                    清空
                </uv-button>

                <uv-button
                    type="primary"
                    @tap="addBatchId"
                    :disabled="!batchId.trim() || searching"
                    size="default"
                >
                    查询
                </uv-button>

                <uv-button
                    type="warning"
                    @tap="showWarehouseModal = true"
                    :disabled="operating || selectedBatches.length === 0"
                    size="default"
                >
                    入库
                </uv-button>
            </view>
        </view>
        
        <!-- 批次列表 -->
        <view class="batch-list" v-if="batchList.length > 0">
            <view class="list-title">
                <text>查询结果 ({{ batchList.length }}个批次)</text>
                <uv-button
                    type="success"
                    size="mini"
                    @tap="selectAll"
                    v-if="!allSelected"
                >
                    全选
                </uv-button>
                <uv-button
                    type="default"
                    size="mini"
                    @tap="unselectAll"
                    v-else
                >
                    取消全选
                </uv-button>
            </view>

            <uv-swipe-action
                v-for="(item, index) in batchList"
                :key="`${item.id || item.lotId}-${item.selected}`"
                :options="getSwipeOptions(index)"
                @click="(optionIndex) => handleSwipeClick(index, optionIndex)"
            >
                <view
                    class="batch-item"
                    :class="{ 'selected': item.selected, 'disabled': item.status === 'SHIP' }"
                >
                    <view class="item-header">
                        <view class="batch-id" @tap="toggleSelect(index)">{{ item.lotId }}</view>
                        <uv-checkbox
                            :modelValue="item.selected"
                            @update:modelValue="(value) => onCheckboxChange(index, value)"
                            @tap.stop
                            shape="circle"
                        ></uv-checkbox>
                    </view>

                    <view class="item-content" @tap="toggleSelect(index)">
                        <uv-row customStyle="margin-bottom: 10rpx">
                            <uv-col :span="6">
                                <text class="label">产品名称:</text>
                                <text class="value">{{ item.productName }}</text>
                            </uv-col>
                            <uv-col :span="6" textAlign="right">
                                <uv-tags
                                    :text="getStatusText(item.status)"
                                    :type="getStatusType(item.status)"
                                    size="mini"
                                ></uv-tags>
                            </uv-col>
                        </uv-row>

                        <uv-row customStyle="margin-bottom: 10rpx">
                            <uv-col :span="6">
                                <text class="label">当前站点:</text>
                                <text class="value">{{ item.currentStationName || '未设置' }}</text>
                            </uv-col>
                            <uv-col :span="6" textAlign="right">
                                <text class="label">颗粒数:</text>
                                <text class="value">{{ item.particleCount || 0 }}</text>
                            </uv-col>
                        </uv-row>
                    </view>

                    <view class="item-status" v-if="item.status === 'SHIP'">
                        <text class="status-text">该批次已入库</text>
                    </view>
                </view>
            </uv-swipe-action>
        </view>

        
        <!-- 空状态 -->
        <uv-empty 
            v-if="batchList.length === 0 && !searching" 
            text="请输入批次ID进行查询"
            icon="search"
        ></uv-empty>
        
        <!-- 入库确认弹窗 -->
        <uv-modal 
            :show="showWarehouseModal" 
            title="确认入库"
            @confirm="confirmWarehouse"
            @cancel="showWarehouseModal = false"
            :showCancelButton="true"
            :asyncClose="true"
        >
            <view class="modal-content">
                <view class="confirm-text">
                    确认将以下 {{ selectedBatches.length }} 个批次进行入库操作？
                </view>
                
                <view class="batch-summary">
                    <view 
                        class="summary-item" 
                        v-for="(batch, index) in selectedBatches" 
                        :key="index"
                    >
                        <text class="summary-id">{{ batch.lotId }}</text>
                        <text class="summary-product">{{ batch.productName }}</text>
                    </view>
                </view>
                
                <view class="remark-section">
                    <view class="remark-label">入库备注（可选）：</view>
                    <uv-textarea 
                        v-model="warehouseRemark" 
                        placeholder="请输入入库备注"
                        :maxlength="200"
                        count
                        height="80"
                    ></uv-textarea>
                </view>
            </view>
        </uv-modal>
    </view>
</template>

<script>
import { searchBatchByLotId, batchWarehouse } from '@/api/choiceway/mesBatchApi.js'

export default {
    data() {
        return {
            batchId: '',
            batchList: [],
            searching: false,
            operating: false,
            showWarehouseModal: false,
            warehouseRemark: ''
        }
    },
    
    computed: {
        // 已选择的批次
        selectedBatches() {
            return this.batchList.filter(item => item.selected && item.status !== 'SHIP')
        },
        
        // 是否全选
        allSelected() {
            const availableBatches = this.batchList.filter(item => item.status !== 'SHIP')
            return availableBatches.length > 0 && availableBatches.every(item => item.selected)
        }
    },
    
    onLoad(options) {
        if (options.batchId) {
            this.batchId = options.batchId
            this.addBatchId()
        }
    },
    
    methods: {
        // 添加批次ID
        async addBatchId() {
            if (!this.batchId.trim()) {
                uni.showToast({
                    title: '请输入批次ID',
                    icon: 'none'
                })
                return
            }

            const trimmedLotId = this.batchId.trim()

            // 检查是否已存在
            const existingBatch = this.batchList.find(item => item.lotId === trimmedLotId)
            if (existingBatch) {
                uni.showToast({
                    title: '该批次已在列表中',
                    icon: 'none'
                })
                this.batchId = ''
                return
            }

            this.searching = true

            try {
                const res = await searchBatchByLotId(trimmedLotId)

                if (res.code === 200 && res.data && res.data.records && res.data.records.length > 0) {
                    // 取第一个匹配的批次（通常lotId是唯一的）
                    const batchData = res.data.records[0]
                    const newBatch = {
                        ...batchData,
                        selected: false // 默认不选中，用户需要手动选择
                    }
                    this.batchList.push(newBatch)
                    this.batchId = '' // 清空输入框

                    uni.showToast({
                        title: '批次添加成功',
                        icon: 'success'
                    })
                } else {
                    uni.showToast({
                        title: '未找到该批次',
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('查询批次失败:', error)
                uni.showToast({
                    title: '查询失败',
                    icon: 'none'
                })
            } finally {
                this.searching = false
            }
        },

        // 扫码功能
        scanCode() {
            uni.scanCode({
                success: (res) => {
                    this.batchId = res.result
                    // 扫码成功后自动添加
                    this.addBatchId()
                },
                fail: (err) => {
                    console.error('扫码失败:', err)
                    uni.showToast({
                        title: '扫码失败',
                        icon: 'none'
                    })
                }
            })
        },
        
        // 清空所有
        clearAll() {
            this.batchId = ''
            this.batchList = []
            this.warehouseRemark = ''
            uni.showToast({
                title: '已清空',
                icon: 'success'
            })
        },



        // 获取左滑选项
        getSwipeOptions(index) {
            return [{
                text: '删除',
                style: {
                    backgroundColor: '#f56c6c'
                }
            }]
        },

        // 处理左滑点击
        handleSwipeClick(index, optionIndex) {
            if (optionIndex === 0) { // 删除选项
                this.removeBatchItem(index)
            }
        },

        // 删除批次项
        removeBatchItem(index) {
            const item = this.batchList[index]
            uni.showModal({
                title: '确认删除',
                content: `确定要从列表中移除批次 ${item.lotId} 吗？`,
                success: (res) => {
                    if (res.confirm) {
                        this.batchList.splice(index, 1)
                        uni.showToast({
                            title: '已移除',
                            icon: 'success'
                        })
                    }
                }
            })
        },
        
        // 切换选择状态
        toggleSelect(index) {
            const item = this.batchList[index]
            console.log('点击切换选择状态:', index, '当前状态:', item.selected, '批次状态:', item.status)
            if (item.status !== 'SHIP') {
                // 直接修改数组中的对象属性
                const newSelected = !item.selected
                this.batchList[index].selected = newSelected
                // 强制更新视图
                this.$forceUpdate()
                console.log('切换后状态:', newSelected)
            }
        },
        
        // 复选框变化
        onCheckboxChange(index, value) {
            const item = this.batchList[index]
            console.log('复选框状态变化:', index, '新值:', value, '当前值:', item.selected)
            if (item.status !== 'SHIP') {
                this.batchList[index].selected = value
                this.$forceUpdate()
                console.log('复选框更新后状态:', this.batchList[index].selected)
            }
        },
        
        // 全选
        selectAll() {
            this.batchList.forEach((item, index) => {
                if (item.status !== 'SHIP') {
                    this.batchList[index].selected = true
                }
            })
            this.$forceUpdate()
        },

        // 取消全选
        unselectAll() {
            this.batchList.forEach((item, index) => {
                this.batchList[index].selected = false
            })
            this.$forceUpdate()
        },
        
        // 确认入库
        async confirmWarehouse() {
            if (this.selectedBatches.length === 0) {
                uni.showToast({
                    title: '请选择要入库的批次',
                    icon: 'none'
                })
                return
            }
            
            this.operating = true
            
            try {
                const batchIds = this.selectedBatches.map(item => item.id)
                
                const res = await batchWarehouse({
                    batchIds: batchIds,
                    remark: this.warehouseRemark
                })
                
                if (res.code === 200) {
                    uni.showToast({
                        title: '入库成功',
                        icon: 'success'
                    })
                    
                    this.showWarehouseModal = false
                    this.warehouseRemark = ''
                    
                    // 更新批次状态为已入库
                    this.selectedBatches.forEach(batch => {
                        batch.status = 'SHIP'
                        batch.selected = false
                    })
                } else {
                    uni.showToast({
                        title: res.message || '入库失败',
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('入库失败:', error)
                uni.showToast({
                    title: '入库失败',
                    icon: 'none'
                })
            } finally {
                this.operating = false
            }
        },
        
        // 获取状态文本
        getStatusText(status) {
            const statusMap = {
                'WAIT': '等待',
                'RUN': '运行中',
                'SHIP': '已入库'
            }
            return statusMap[status] || status
        },
        
        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'WAIT': 'warning',
                'RUN': 'success',
                'SHIP': 'info'
            }
            return typeMap[status] || 'default'
        }
    }
}
</script>

<style scoped>
.warehouse-container {
    padding: 20rpx;
    background: #f5f5f5;
    min-height: 100vh;
}

.search-section {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
}

.search-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
}

.input-wrapper {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 0 20rpx;
    margin-bottom: 20rpx;
    height: 80rpx;
}

.input-wrapper .icon {
    margin-right: 15rpx;
}

.input-wrapper .input {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    background: transparent;
    border: none;
    outline: none;
}

.input-wrapper .input::placeholder {
    color: #999;
}

.scan-btn {
    padding: 10rpx;
    margin-left: 15rpx;
    border-radius: 6rpx;
    background: #f0f4ff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-buttons-top {
    display: flex;
    gap: 15rpx;
    justify-content: space-between;
}

.batch-list {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
}

.list-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

.batch-item {
    border: 2rpx solid #e4e7ed;
    border-radius: 8rpx;
    padding: 20rpx;
    margin-bottom: 15rpx;
    transition: all 0.3s;
}

.batch-item.selected {
    border-color: #5677fc;
    background: #f0f4ff;
}

.batch-item.disabled {
    opacity: 0.6;
    background: #f5f5f5;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
}

.batch-id {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
}

.item-content .label {
    font-size: 26rpx;
    color: #666;
    margin-right: 10rpx;
}

.item-content .value {
    font-size: 26rpx;
    color: #333;
}

.item-status {
    text-align: center;
    margin-top: 15rpx;
    padding-top: 15rpx;
    border-top: 1rpx solid #e4e7ed;
}

.status-text {
    font-size: 24rpx;
    color: #909399;
}



.modal-content {
    padding: 20rpx 0;
}

.confirm-text {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
}

.batch-summary {
    max-height: 200rpx;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 8rpx;
    padding: 15rpx;
    margin-bottom: 20rpx;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 8rpx 0;
    border-bottom: 1rpx solid #e4e7ed;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-id {
    font-size: 26rpx;
    font-weight: bold;
    color: #333;
}

.summary-product {
    font-size: 24rpx;
    color: #666;
}

.remark-section {
    margin-top: 20rpx;
}

.remark-label {
    font-size: 26rpx;
    color: #333;
    margin-bottom: 10rpx;
}
</style>
